import React, { useState } from 'react';

function App() {
  // const { profile, isProfileComplete, setProfile } = useUserStore();
  const [showTest, setShowTest] = useState(true);

  if (showTest) {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1 style={{ color: '#333' }}>FitAI - Fitness App</h1>
        <p>Welcome to your AI-powered fitness companion!</p>
        <button
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
          onClick={() => {
            alert('Profile creation will be implemented next!');
            setShowTest(false);
          }}
        >
          Create Profile & Continue
        </button>
        <button
          style={{
            backgroundColor: '#6b7280',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
          onClick={() => alert('Store will be tested next!')}
        >
          Test Store
        </button>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#333' }}>FitAI Dashboard</h1>
      <div style={{ backgroundColor: '#f3f4f6', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
        <h2>Dashboard Coming Soon!</h2>
        <p>This will show your fitness progress, AI recommendations, and more.</p>
      </div>
      <button
        style={{
          backgroundColor: '#ef4444',
          color: 'white',
          padding: '10px 20px',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}
        onClick={() => {
          setShowTest(true);
        }}
      >
        Back to Test
      </button>
    </div>
  );
}

export default App;
