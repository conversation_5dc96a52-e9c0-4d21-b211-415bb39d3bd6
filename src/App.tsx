import React, { useState } from 'react';

interface UserProfile {
  age: number;
  gender: 'male' | 'female' | 'other';
  height: number;
  weight: number;
  activityLevel: string;
  goal: string;
  bodyType?: string;
}

function App() {
  const [currentPage, setCurrentPage] = useState<'welcome' | 'profile' | 'dashboard'>('welcome');
  const [profile, setProfile] = useState<UserProfile | null>(null);

  if (currentPage === 'welcome') {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', maxWidth: '600px', margin: '0 auto' }}>
        <h1 style={{ color: '#333', textAlign: 'center' }}>🏋️ FitAI - Your AI Fitness Companion</h1>
        <p style={{ textAlign: 'center', fontSize: '18px', color: '#666' }}>
          Get personalized diet plans, workout recommendations, and track your progress with AI-powered insights!
        </p>

        <div style={{ backgroundColor: '#f8fafc', padding: '20px', borderRadius: '8px', margin: '20px 0' }}>
          <h3>🚀 Features:</h3>
          <ul style={{ color: '#555' }}>
            <li>📸 AI-powered food recognition and calorie tracking</li>
            <li>🎯 Personalized diet plans based on your body type</li>
            <li>💪 Custom workout recommendations</li>
            <li>📊 Progress tracking with smart adjustments</li>
            <li>🧠 Gemini AI integration for intelligent recommendations</li>
          </ul>
        </div>

        <div style={{ textAlign: 'center' }}>
          <button
            style={{
              backgroundColor: '#3b82f6',
              color: 'white',
              padding: '15px 30px',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: 'bold'
            }}
            onClick={() => setCurrentPage('profile')}
          >
            Get Started - Create Your Profile
          </button>
        </div>
      </div>
    );
  }

  if (currentPage === 'profile') {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', maxWidth: '500px', margin: '0 auto' }}>
        <h1 style={{ color: '#333', textAlign: 'center' }}>Create Your Profile</h1>
        <p style={{ textAlign: 'center', color: '#666' }}>Tell us about yourself to get personalized recommendations</p>

        <form onSubmit={(e) => {
          e.preventDefault();
          const formData = new FormData(e.target as HTMLFormElement);
          const newProfile: UserProfile = {
            age: parseInt(formData.get('age') as string),
            gender: formData.get('gender') as 'male' | 'female' | 'other',
            height: parseInt(formData.get('height') as string),
            weight: parseFloat(formData.get('weight') as string),
            activityLevel: formData.get('activityLevel') as string,
            goal: formData.get('goal') as string,
            bodyType: 'mesomorph' // AI will determine this later
          };
          setProfile(newProfile);
          setCurrentPage('dashboard');
        }}>
          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Age:</label>
            <input type="number" name="age" required style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }} />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Gender:</label>
            <select name="gender" required style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}>
              <option value="">Select gender</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Height (cm):</label>
            <input type="number" name="height" required style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }} />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Weight (kg):</label>
            <input type="number" step="0.1" name="weight" required style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }} />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Activity Level:</label>
            <select name="activityLevel" required style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}>
              <option value="">Select activity level</option>
              <option value="sedentary">Sedentary (little or no exercise)</option>
              <option value="lightly_active">Lightly Active (light exercise 1-3 days/week)</option>
              <option value="moderately_active">Moderately Active (moderate exercise 3-5 days/week)</option>
              <option value="very_active">Very Active (hard exercise 6-7 days/week)</option>
              <option value="extremely_active">Extremely Active (very hard exercise, physical job)</option>
            </select>
          </div>

          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Goal:</label>
            <select name="goal" required style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}>
              <option value="">Select your goal</option>
              <option value="lose_weight">Lose Weight</option>
              <option value="maintain_weight">Maintain Weight</option>
              <option value="gain_weight">Gain Weight</option>
              <option value="build_muscle">Build Muscle</option>
            </select>
          </div>

          <div style={{ textAlign: 'center' }}>
            <button
              type="button"
              onClick={() => setCurrentPage('welcome')}
              style={{
                backgroundColor: '#6b7280',
                color: 'white',
                padding: '10px 20px',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer',
                marginRight: '10px'
              }}
            >
              Back
            </button>
            <button
              type="submit"
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                padding: '10px 20px',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
            >
              Create Profile
            </button>
          </div>
        </form>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#333' }}>🏋️ FitAI Dashboard</h1>
      <div style={{ backgroundColor: '#f3f4f6', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
        <h2>Welcome, {profile?.gender === 'male' ? 'Sir' : profile?.gender === 'female' ? 'Ma\'am' : 'Friend'}!</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px', marginTop: '15px' }}>
          <div style={{ backgroundColor: 'white', padding: '15px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#3b82f6' }}>📊 Your Stats</h3>
            <p><strong>Age:</strong> {profile?.age}</p>
            <p><strong>Height:</strong> {profile?.height} cm</p>
            <p><strong>Weight:</strong> {profile?.weight} kg</p>
            <p><strong>BMI:</strong> {profile ? (profile.weight / ((profile.height / 100) ** 2)).toFixed(1) : 'N/A'}</p>
          </div>
          <div style={{ backgroundColor: 'white', padding: '15px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#10b981' }}>🎯 Your Goal</h3>
            <p><strong>Goal:</strong> {profile?.goal?.replace('_', ' ')}</p>
            <p><strong>Activity:</strong> {profile?.activityLevel?.replace('_', ' ')}</p>
            <p><strong>Body Type:</strong> {profile?.bodyType}</p>
          </div>
        </div>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
          <h3 style={{ color: '#3b82f6' }}>📱 Calorie Tracker</h3>
          <p>Track your daily food intake with AI-powered food recognition</p>
          <button style={{ backgroundColor: '#3b82f6', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}>
            Coming Soon
          </button>
        </div>

        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
          <h3 style={{ color: '#f59e0b' }}>💪 Workout Plans</h3>
          <p>Get personalized workout recommendations based on your goals</p>
          <button style={{ backgroundColor: '#f59e0b', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}>
            Coming Soon
          </button>
        </div>

        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
          <h3 style={{ color: '#10b981' }}>🍽️ Diet Plans</h3>
          <p>AI-generated meal plans tailored to your body type and goals</p>
          <button style={{ backgroundColor: '#10b981', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}>
            Coming Soon
          </button>
        </div>
      </div>

      <div style={{ textAlign: 'center', marginTop: '30px' }}>
        <button
          style={{
            backgroundColor: '#ef4444',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
          onClick={() => {
            setProfile(null);
            setCurrentPage('welcome');
          }}
        >
          Reset Profile
        </button>
      </div>
    </div>
  );
}

export default App;
