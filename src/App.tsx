import React, { useState, useEffect } from 'react';
import { geminiService } from './geminiService.js';

// Modern Design System
const theme = {
  colors: {
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
      900: '#0c4a6e'
    },
    secondary: {
      50: '#fdf4ff',
      100: '#fae8ff',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7c3aed'
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d'
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      500: '#f59e0b',
      600: '#d97706'
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      500: '#ef4444',
      600: '#dc2626'
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827'
    }
  },
  gradients: {
    primary: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',
    secondary: 'linear-gradient(135deg, #a855f7 0%, #9333ea 100%)',
    success: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
    warm: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  }
};

interface UserProfile {
  age: number;
  gender: 'male' | 'female' | 'other';
  height: number;
  weight: number;
  activityLevel: string;
  goal: string;
  bodyType?: string;
}

interface FoodEntry {
  id: string;
  date: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  meal: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

interface WeightEntry {
  date: string;
  weight: number;
  notes?: string;
}

function App() {
  const [currentPage, setCurrentPage] = useState<'welcome' | 'profile' | 'dashboard' | 'calories' | 'workouts' | 'diet' | 'weight'>('welcome');
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [foodEntries, setFoodEntries] = useState<FoodEntry[]>([]);
  const [weightHistory, setWeightHistory] = useState<WeightEntry[]>([]);
  const [dietPlan, setDietPlan] = useState<any>(null);
  const [workoutPlan, setWorkoutPlan] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Helper functions
  const getTodayCalories = () => {
    const today = new Date().toISOString().split('T')[0];
    return foodEntries
      .filter(entry => entry.date === today)
      .reduce((sum, entry) => sum + entry.calories, 0);
  };

  const addFoodEntry = (food: Omit<FoodEntry, 'id'>) => {
    const newEntry: FoodEntry = {
      ...food,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9)
    };
    setFoodEntries(prev => [...prev, newEntry]);
  };

  const addWeightEntry = (entry: WeightEntry) => {
    setWeightHistory(prev => {
      const filtered = prev.filter(w => w.date !== entry.date);
      return [...filtered, entry].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    });
  };

  if (currentPage === 'welcome') {
    return (
      <div style={{
        minHeight: '100vh',
        background: theme.gradients.primary,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: 'white',
          borderRadius: '24px',
          padding: '40px',
          boxShadow: theme.shadows.xl,
          textAlign: 'center'
        }}>
          {/* Logo and Title */}
          <div style={{ marginBottom: '32px' }}>
            <div style={{
              fontSize: '64px',
              marginBottom: '16px',
              background: theme.gradients.primary,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              🏋️
            </div>
            <h1 style={{
              color: theme.colors.gray[900],
              margin: '0 0 8px 0',
              fontSize: '32px',
              fontWeight: '700',
              letterSpacing: '-0.025em'
            }}>
              FitAI
            </h1>
            <p style={{
              color: theme.colors.gray[600],
              margin: '0',
              fontSize: '18px',
              fontWeight: '400'
            }}>
              Your AI-Powered Fitness Companion
            </p>
          </div>

          {/* Features Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '16px',
            marginBottom: '32px'
          }}>
            {[
              { icon: '📸', title: 'Smart Food Tracking', desc: 'AI photo recognition' },
              { icon: '🎯', title: 'Personalized Plans', desc: 'Based on body type' },
              { icon: '💪', title: 'Custom Workouts', desc: 'Tailored exercises' },
              { icon: '📊', title: 'Progress Insights', desc: 'Smart adjustments' }
            ].map((feature, index) => (
              <div key={index} style={{
                backgroundColor: theme.colors.gray[50],
                padding: '20px',
                borderRadius: '16px',
                border: `1px solid ${theme.colors.gray[200]}`
              }}>
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>{feature.icon}</div>
                <h3 style={{
                  fontSize: '14px',
                  fontWeight: '600',
                  color: theme.colors.gray[900],
                  margin: '0 0 4px 0'
                }}>
                  {feature.title}
                </h3>
                <p style={{
                  fontSize: '12px',
                  color: theme.colors.gray[600],
                  margin: '0'
                }}>
                  {feature.desc}
                </p>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <button
            style={{
              background: theme.gradients.primary,
              color: 'white',
              padding: '16px 32px',
              border: 'none',
              borderRadius: '16px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: '600',
              width: '100%',
              boxShadow: theme.shadows.lg,
              transition: 'all 0.2s ease'
            }}
            onClick={() => setCurrentPage('profile')}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = theme.shadows.xl;
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = theme.shadows.lg;
            }}
          >
            Get Started - Create Your Profile
          </button>

          {/* Trust Indicators */}
          <div style={{
            marginTop: '24px',
            padding: '16px',
            backgroundColor: theme.colors.success[50],
            borderRadius: '12px',
            border: `1px solid ${theme.colors.success[200]}`
          }}>
            <p style={{
              fontSize: '14px',
              color: theme.colors.success[700],
              margin: '0',
              fontWeight: '500'
            }}>
              🤖 Powered by Google Gemini AI • 🔒 Your data stays private
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (currentPage === 'profile') {
    return (
      <div style={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.colors.gray[50]} 0%, ${theme.colors.gray[100]} 100%)`,
        padding: '20px',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        <div style={{ maxWidth: '500px', margin: '0 auto' }}>
          {/* Header */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '24px',
            padding: '32px',
            marginBottom: '24px',
            boxShadow: theme.shadows.lg,
            textAlign: 'center'
          }}>
            <button
              onClick={() => setCurrentPage('welcome')}
              style={{
                position: 'absolute',
                left: '32px',
                top: '32px',
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: theme.colors.gray[600]
              }}
            >
              ←
            </button>
            <div style={{
              fontSize: '48px',
              marginBottom: '16px',
              background: theme.gradients.secondary,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              👤
            </div>
            <h1 style={{
              color: theme.colors.gray[900],
              margin: '0 0 8px 0',
              fontSize: '28px',
              fontWeight: '700'
            }}>
              Create Your Profile
            </h1>
            <p style={{
              color: theme.colors.gray[600],
              margin: '0',
              fontSize: '16px'
            }}>
              Tell us about yourself for personalized AI recommendations
            </p>
          </div>

          {/* Form */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '24px',
            padding: '32px',
            boxShadow: theme.shadows.lg
          }}>
            <form onSubmit={async (e) => {
              e.preventDefault();
              setIsLoading(true);

              const formData = new FormData(e.target as HTMLFormElement);
              const newProfile: UserProfile = {
                age: parseInt(formData.get('age') as string),
                gender: formData.get('gender') as 'male' | 'female' | 'other',
                height: parseInt(formData.get('height') as string),
                weight: parseFloat(formData.get('weight') as string),
                activityLevel: formData.get('activityLevel') as string,
                goal: formData.get('goal') as string,
              };

              // AI body type analysis
              try {
                const bodyType = await geminiService.analyzeBodyType(newProfile);
                newProfile.bodyType = bodyType;

                // Generate initial diet and workout plans
                const [dietPlanResult, workoutPlanResult] = await Promise.all([
                  geminiService.generateDietPlan(newProfile, weightHistory),
                  geminiService.generateWorkoutPlan(newProfile)
                ]);

                setDietPlan(dietPlanResult);
                setWorkoutPlan(workoutPlanResult);
              } catch (error) {
                console.error('Error with AI analysis:', error);
                newProfile.bodyType = 'mesomorph';
              }

              setProfile(newProfile);
              setIsLoading(false);
              setCurrentPage('dashboard');
            }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: theme.colors.gray[700],
                    fontSize: '14px'
                  }}>
                    Age
                  </label>
                  <input
                    type="number"
                    name="age"
                    required
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      border: `2px solid ${theme.colors.gray[200]}`,
                      fontSize: '16px',
                      transition: 'border-color 0.2s ease',
                      outline: 'none'
                    }}
                    onFocus={(e) => e.target.style.borderColor = theme.colors.primary[500]}
                    onBlur={(e) => e.target.style.borderColor = theme.colors.gray[200]}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: theme.colors.gray[700],
                    fontSize: '14px'
                  }}>
                    Gender
                  </label>
                  <select
                    name="gender"
                    required
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      border: `2px solid ${theme.colors.gray[200]}`,
                      fontSize: '16px',
                      backgroundColor: 'white',
                      outline: 'none'
                    }}
                    onFocus={(e) => e.target.style.borderColor = theme.colors.primary[500]}
                    onBlur={(e) => e.target.style.borderColor = theme.colors.gray[200]}
                  >
                    <option value="">Select gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: theme.colors.gray[700],
                    fontSize: '14px'
                  }}>
                    Height (cm)
                  </label>
                  <input
                    type="number"
                    name="height"
                    required
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      border: `2px solid ${theme.colors.gray[200]}`,
                      fontSize: '16px',
                      outline: 'none'
                    }}
                    onFocus={(e) => e.target.style.borderColor = theme.colors.primary[500]}
                    onBlur={(e) => e.target.style.borderColor = theme.colors.gray[200]}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: theme.colors.gray[700],
                    fontSize: '14px'
                  }}>
                    Weight (kg)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    name="weight"
                    required
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      border: `2px solid ${theme.colors.gray[200]}`,
                      fontSize: '16px',
                      outline: 'none'
                    }}
                    onFocus={(e) => e.target.style.borderColor = theme.colors.primary[500]}
                    onBlur={(e) => e.target.style.borderColor = theme.colors.gray[200]}
                  />
                </div>
              </div>

          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Activity Level:</label>
            <select name="activityLevel" required style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}>
              <option value="">Select activity level</option>
              <option value="sedentary">Sedentary (little or no exercise)</option>
              <option value="lightly_active">Lightly Active (light exercise 1-3 days/week)</option>
              <option value="moderately_active">Moderately Active (moderate exercise 3-5 days/week)</option>
              <option value="very_active">Very Active (hard exercise 6-7 days/week)</option>
              <option value="extremely_active">Extremely Active (very hard exercise, physical job)</option>
            </select>
          </div>

          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Goal:</label>
            <select name="goal" required style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}>
              <option value="">Select your goal</option>
              <option value="lose_weight">Lose Weight</option>
              <option value="maintain_weight">Maintain Weight</option>
              <option value="gain_weight">Gain Weight</option>
              <option value="build_muscle">Build Muscle</option>
            </select>
          </div>

          <div style={{ textAlign: 'center' }}>
            <button
              type="button"
              onClick={() => setCurrentPage('welcome')}
              style={{
                backgroundColor: '#6b7280',
                color: 'white',
                padding: '10px 20px',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer',
                marginRight: '10px'
              }}
            >
              Back
            </button>
              <button
                type="submit"
                disabled={isLoading}
                style={{
                  background: isLoading ? theme.colors.gray[400] : theme.gradients.primary,
                  color: 'white',
                  padding: '16px 32px',
                  border: 'none',
                  borderRadius: '16px',
                  cursor: isLoading ? 'not-allowed' : 'pointer',
                  fontSize: '16px',
                  fontWeight: '600',
                  width: '100%',
                  boxShadow: theme.shadows.lg,
                  transition: 'all 0.2s ease'
                }}
              >
                {isLoading ? (
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <div style={{
                      width: '20px',
                      height: '20px',
                      border: '2px solid transparent',
                      borderTop: '2px solid white',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                    🤖 AI Analyzing Your Profile...
                  </div>
                ) : (
                  '✨ Create My AI-Powered Profile'
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
          </div>
        </form>
      </div>
    );
  }

  // Calorie Tracker Page - Cal AI Style
  if (currentPage === 'calories') {
    const today = new Date().toISOString().split('T')[0];
    const todayFoods = foodEntries.filter(entry => entry.date === today);
    const todayCalories = todayFoods.reduce((sum, entry) => sum + entry.calories, 0);
    const todayProtein = todayFoods.reduce((sum, entry) => sum + entry.protein, 0);
    const todayCarbs = todayFoods.reduce((sum, entry) => sum + entry.carbs, 0);
    const todayFat = todayFoods.reduce((sum, entry) => sum + entry.fat, 0);

    const targetCalories = dietPlan?.dailyCalories || 2000;
    const targetProtein = dietPlan?.macros?.protein || 150;
    const targetCarbs = dietPlan?.macros?.carbs || 200;
    const targetFat = dietPlan?.macros?.fat || 65;

    const mealGroups = {
      breakfast: todayFoods.filter(f => f.meal === 'breakfast'),
      lunch: todayFoods.filter(f => f.meal === 'lunch'),
      dinner: todayFoods.filter(f => f.meal === 'dinner'),
      snack: todayFoods.filter(f => f.meal === 'snack')
    };

    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', maxWidth: '400px', margin: '0 auto', backgroundColor: '#f8fafc', minHeight: '100vh' }}>
        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <button
            onClick={() => setCurrentPage('dashboard')}
            style={{ backgroundColor: 'transparent', border: 'none', fontSize: '24px', cursor: 'pointer' }}
          >
            ←
          </button>
          <h1 style={{ color: '#333', margin: '0', fontSize: '20px' }}>Today</h1>
          <div style={{ width: '24px' }}></div>
        </div>

        {/* Calorie Ring - Cal AI Style */}
        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <div style={{ position: 'relative', display: 'inline-block' }}>
            <svg width="200" height="200" style={{ transform: 'rotate(-90deg)' }}>
              {/* Background circle */}
              <circle
                cx="100"
                cy="100"
                r="80"
                fill="none"
                stroke="#e5e7eb"
                strokeWidth="12"
              />
              {/* Progress circle */}
              <circle
                cx="100"
                cy="100"
                r="80"
                fill="none"
                stroke="#10b981"
                strokeWidth="12"
                strokeDasharray={`${2 * Math.PI * 80}`}
                strokeDashoffset={`${2 * Math.PI * 80 * (1 - Math.min(todayCalories / targetCalories, 1))}`}
                strokeLinecap="round"
              />
            </svg>
            <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', textAlign: 'center' }}>
              <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#333' }}>{todayCalories}</div>
              <div style={{ fontSize: '14px', color: '#666' }}>of {targetCalories}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>calories</div>
            </div>
          </div>
        </div>

        {/* Macro Breakdown */}
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '10px', marginBottom: '30px' }}>
          <div style={{ textAlign: 'center', backgroundColor: 'white', padding: '15px', borderRadius: '12px' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#3b82f6' }}>{todayProtein.toFixed(0)}g</div>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Protein</div>
            <div style={{ backgroundColor: '#e5e7eb', height: '4px', borderRadius: '2px' }}>
              <div style={{ backgroundColor: '#3b82f6', height: '100%', borderRadius: '2px', width: `${Math.min((todayProtein / targetProtein) * 100, 100)}%` }}></div>
            </div>
          </div>
          <div style={{ textAlign: 'center', backgroundColor: 'white', padding: '15px', borderRadius: '12px' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#f59e0b' }}>{todayCarbs.toFixed(0)}g</div>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Carbs</div>
            <div style={{ backgroundColor: '#e5e7eb', height: '4px', borderRadius: '2px' }}>
              <div style={{ backgroundColor: '#f59e0b', height: '100%', borderRadius: '2px', width: `${Math.min((todayCarbs / targetCarbs) * 100, 100)}%` }}></div>
            </div>
          </div>
          <div style={{ textAlign: 'center', backgroundColor: 'white', padding: '15px', borderRadius: '12px' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#ef4444' }}>{todayFat.toFixed(0)}g</div>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Fat</div>
            <div style={{ backgroundColor: '#e5e7eb', height: '4px', borderRadius: '2px' }}>
              <div style={{ backgroundColor: '#ef4444', height: '100%', borderRadius: '2px', width: `${Math.min((todayFat / targetFat) * 100, 100)}%` }}></div>
            </div>
          </div>
        </div>

        {/* Add Food Button - Cal AI Style */}
        <div style={{ marginBottom: '30px' }}>
          <label style={{
            display: 'block',
            backgroundColor: '#10b981',
            color: 'white',
            padding: '15px',
            borderRadius: '12px',
            textAlign: 'center',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}>
            📸 Snap a photo to add food
            <input
              type="file"
              accept="image/*"
              capture="environment"
              style={{ display: 'none' }}
              onChange={async (e) => {
                const file = e.target.files?.[0];
                if (!file) return;

                setIsLoading(true);
                const reader = new FileReader();
                reader.onload = async (event) => {
                  const base64 = event.target?.result as string;
                  const base64Data = base64.split(',')[1];

                  try {
                    // Enhanced food analysis prompt for better accuracy
                    const enhancedPrompt = `
                    Analyze this food image in detail. Estimate the portion size and provide accurate nutritional information.

                    Look for:
                    - Type of food and ingredients
                    - Portion size (small, medium, large, or estimate weight/volume)
                    - Cooking method (fried, grilled, baked, etc.)
                    - Any visible condiments or toppings

                    Return a JSON object with:
                    {
                      "food": "Detailed food name with portion estimate",
                      "calories": number (realistic estimate based on portion),
                      "confidence": number (0-100, how confident you are),
                      "portion": "estimated portion size",
                      "macros": {
                        "protein": number (in grams),
                        "carbs": number (in grams),
                        "fat": number (in grams)
                      },
                      "suggestions": ["alternative portion sizes if uncertain"]
                    }
                    `;

                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent?key=AIzaSyCQz5-VAVuj03vXnaOx8YLsLTWb_mptUvQ`, {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        contents: [{
                          parts: [
                            { text: enhancedPrompt },
                            { inline_data: { mime_type: 'image/jpeg', data: base64Data } }
                          ]
                        }]
                      })
                    });

                    const data = await response.json();
                    const text = data.candidates[0].content.parts[0].text;
                    const jsonMatch = text.match(/\{[\s\S]*\}/);

                    if (jsonMatch) {
                      const analysis = JSON.parse(jsonMatch[0]);

                      // Show confirmation dialog with portion adjustment
                      const confirmed = confirm(`
🤖 AI detected: ${analysis.food}
📊 Estimated: ${analysis.calories} calories
🎯 Confidence: ${analysis.confidence}%
📏 Portion: ${analysis.portion}

Macros:
• Protein: ${analysis.macros.protein}g
• Carbs: ${analysis.macros.carbs}g
• Fat: ${analysis.macros.fat}g

Click OK to add, or Cancel to adjust manually.
                      `);

                      if (confirmed) {
                        // Auto-detect meal time
                        const hour = new Date().getHours();
                        let mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack' = 'snack';
                        if (hour >= 6 && hour < 11) mealType = 'breakfast';
                        else if (hour >= 11 && hour < 16) mealType = 'lunch';
                        else if (hour >= 16 && hour < 22) mealType = 'dinner';

                        addFoodEntry({
                          date: today,
                          name: analysis.food,
                          calories: analysis.calories,
                          protein: analysis.macros.protein,
                          carbs: analysis.macros.carbs,
                          fat: analysis.macros.fat,
                          meal: mealType
                        });
                      }
                    } else {
                      throw new Error('Could not parse AI response');
                    }
                  } catch (error) {
                    console.error('Error:', error);
                    alert('🤖 AI had trouble analyzing this image. Try a clearer photo or add manually.');
                  }
                  setIsLoading(false);
                };
                reader.readAsDataURL(file);
              }}
            />
          </label>
          {isLoading && (
            <div style={{ textAlign: 'center', marginTop: '10px', color: '#666' }}>
              <div style={{ fontSize: '20px', marginBottom: '5px' }}>🤖</div>
              <div>AI analyzing your food...</div>
            </div>
          )}
        </div>

        {/* Meals by Time - Cal AI Style */}
        {Object.entries(mealGroups).map(([mealType, foods]) => {
          const mealCalories = foods.reduce((sum, food) => sum + food.calories, 0);
          const mealEmojis = { breakfast: '🌅', lunch: '☀️', dinner: '🌙', snack: '🍎' };

          return (
            <div key={mealType} style={{ marginBottom: '20px' }}>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '15px',
                border: foods.length > 0 ? '2px solid #10b981' : '1px solid #e5e7eb'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ fontSize: '20px' }}>{mealEmojis[mealType as keyof typeof mealEmojis]}</span>
                    <span style={{ fontSize: '16px', fontWeight: 'bold', textTransform: 'capitalize' }}>{mealType}</span>
                  </div>
                  <span style={{ fontSize: '14px', color: '#666', fontWeight: 'bold' }}>{mealCalories} cal</span>
                </div>

                {foods.length === 0 ? (
                  <button
                    onClick={() => {
                      const foodName = prompt(`Add food to ${mealType}:`);
                      if (foodName) {
                        const calories = parseInt(prompt('Estimated calories:') || '100');
                        addFoodEntry({
                          date: today,
                          name: foodName,
                          calories,
                          protein: Math.round(calories * 0.15 / 4),
                          carbs: Math.round(calories * 0.5 / 4),
                          fat: Math.round(calories * 0.35 / 9),
                          meal: mealType as 'breakfast' | 'lunch' | 'dinner' | 'snack'
                        });
                      }
                    }}
                    style={{
                      width: '100%',
                      padding: '10px',
                      backgroundColor: '#f3f4f6',
                      border: '1px dashed #d1d5db',
                      borderRadius: '8px',
                      color: '#666',
                      cursor: 'pointer'
                    }}
                  >
                    + Add {mealType}
                  </button>
                ) : (
                  <div style={{ display: 'grid', gap: '8px' }}>
                    {foods.map(food => (
                      <div key={food.id} style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '8px',
                        backgroundColor: '#f8fafc',
                        borderRadius: '6px'
                      }}>
                        <div>
                          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>{food.name}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            P: {food.protein}g • C: {food.carbs}g • F: {food.fat}g
                          </div>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <span style={{ fontSize: '14px', fontWeight: 'bold' }}>{food.calories}</span>
                          <button
                            onClick={() => setFoodEntries(prev => prev.filter(f => f.id !== food.id))}
                            style={{
                              backgroundColor: '#ef4444',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              padding: '4px 8px',
                              fontSize: '12px',
                              cursor: 'pointer'
                            }}
                          >
                            ×
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}

        {/* Quick Add Suggestions */}
        <div style={{ marginTop: '20px' }}>
          <h3 style={{ fontSize: '16px', marginBottom: '10px' }}>Quick Add</h3>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
            {[
              { name: 'Water (8oz)', calories: 0 },
              { name: 'Coffee (black)', calories: 5 },
              { name: 'Apple (medium)', calories: 95 },
              { name: 'Banana (medium)', calories: 105 }
            ].map(item => (
              <button
                key={item.name}
                onClick={() => {
                  addFoodEntry({
                    date: today,
                    name: item.name,
                    calories: item.calories,
                    protein: 0,
                    carbs: item.calories / 4,
                    fat: 0,
                    meal: 'snack'
                  });
                }}
                style={{
                  padding: '8px',
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                {item.name}
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Workout Plans Page
  if (currentPage === 'workouts') {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', maxWidth: '800px', margin: '0 auto' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h1 style={{ color: '#333' }}>💪 Workout Plans</h1>
          <button
            onClick={() => setCurrentPage('dashboard')}
            style={{ backgroundColor: '#6b7280', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            ← Back to Dashboard
          </button>
        </div>

        {workoutPlan ? (
          <div>
            <div style={{ backgroundColor: '#f3f4f6', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
              <h2>{workoutPlan.name}</h2>
              <p><strong>Duration:</strong> {workoutPlan.duration} minutes</p>
              <p><strong>Difficulty:</strong> {workoutPlan.difficulty}</p>
              <p><strong>Total Exercises:</strong> {workoutPlan.exercises?.length || 0}</p>
            </div>

            <div style={{ display: 'grid', gap: '15px' }}>
              {workoutPlan.exercises?.map((exercise: any, index: number) => (
                <div key={index} style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
                  <h3 style={{ color: '#f59e0b', margin: '0 0 10px 0' }}>{exercise.name}</h3>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '10px', marginBottom: '10px' }}>
                    <p><strong>Sets:</strong> {exercise.sets}</p>
                    <p><strong>Reps:</strong> {exercise.reps}</p>
                    {exercise.duration && <p><strong>Duration:</strong> {exercise.duration} min</p>}
                    <p><strong>Calories:</strong> ~{exercise.calories}</p>
                  </div>
                  {exercise.instructions && (
                    <div>
                      <h4>Instructions:</h4>
                      <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                        {exercise.instructions.map((instruction: string, i: number) => (
                          <li key={i} style={{ marginBottom: '5px' }}>{instruction}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div style={{ textAlign: 'center', marginTop: '20px' }}>
              <button
                onClick={async () => {
                  if (!profile) return;
                  setIsLoading(true);
                  try {
                    const newWorkoutPlan = await geminiService.generateWorkoutPlan(profile);
                    setWorkoutPlan(newWorkoutPlan);
                  } catch (error) {
                    alert('Error generating new workout plan');
                  }
                  setIsLoading(false);
                }}
                disabled={isLoading}
                style={{
                  backgroundColor: isLoading ? '#9ca3af' : '#f59e0b',
                  color: 'white',
                  padding: '10px 20px',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: isLoading ? 'not-allowed' : 'pointer'
                }}
              >
                {isLoading ? '🤖 Generating...' : '🔄 Generate New Plan'}
              </button>
            </div>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <p>Generating your personalized workout plan...</p>
            <div style={{ fontSize: '24px' }}>🤖</div>
          </div>
        )}
      </div>
    );
  }

  // Diet Plans Page
  if (currentPage === 'diet') {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', maxWidth: '800px', margin: '0 auto' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h1 style={{ color: '#333' }}>🍽️ Diet Plans</h1>
          <button
            onClick={() => setCurrentPage('dashboard')}
            style={{ backgroundColor: '#6b7280', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            ← Back to Dashboard
          </button>
        </div>

        {dietPlan ? (
          <div>
            <div style={{ backgroundColor: '#f3f4f6', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
              <h2>Your Personalized Diet Plan</h2>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '15px' }}>
                <div>
                  <p><strong>Daily Calories:</strong></p>
                  <p style={{ fontSize: '24px', color: '#10b981', margin: '0' }}>{dietPlan.dailyCalories}</p>
                </div>
                <div>
                  <p><strong>Protein:</strong></p>
                  <p style={{ fontSize: '20px', color: '#3b82f6', margin: '0' }}>{dietPlan.macros?.protein}g</p>
                </div>
                <div>
                  <p><strong>Carbs:</strong></p>
                  <p style={{ fontSize: '20px', color: '#f59e0b', margin: '0' }}>{dietPlan.macros?.carbs}g</p>
                </div>
                <div>
                  <p><strong>Fat:</strong></p>
                  <p style={{ fontSize: '20px', color: '#ef4444', margin: '0' }}>{dietPlan.macros?.fat}g</p>
                </div>
              </div>
            </div>

            <h3>Recommended Meals</h3>
            <div style={{ display: 'grid', gap: '15px', marginBottom: '20px' }}>
              {dietPlan.meals?.map((meal: any, index: number) => (
                <div key={index} style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
                  <h3 style={{ color: '#10b981', margin: '0 0 10px 0' }}>{meal.name}</h3>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(100px, 1fr))', gap: '10px', marginBottom: '15px' }}>
                    <p><strong>Calories:</strong> {meal.calories}</p>
                    <p><strong>Protein:</strong> {meal.protein}g</p>
                    <p><strong>Carbs:</strong> {meal.carbs}g</p>
                    <p><strong>Fat:</strong> {meal.fat}g</p>
                  </div>

                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
                    <div>
                      <h4>Ingredients:</h4>
                      <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                        {meal.ingredients?.map((ingredient: string, i: number) => (
                          <li key={i}>{ingredient}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4>Instructions:</h4>
                      <ol style={{ margin: '5px 0', paddingLeft: '20px' }}>
                        {meal.instructions?.map((instruction: string, i: number) => (
                          <li key={i} style={{ marginBottom: '5px' }}>{instruction}</li>
                        ))}
                      </ol>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {dietPlan.tips && (
              <div style={{ backgroundColor: '#fef3c7', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h4 style={{ color: '#92400e', margin: '0 0 10px 0' }}>💡 Tips for Success:</h4>
                <ul style={{ margin: '0', paddingLeft: '20px', color: '#92400e' }}>
                  {dietPlan.tips.map((tip: string, index: number) => (
                    <li key={index}>{tip}</li>
                  ))}
                </ul>
              </div>
            )}

            <div style={{ textAlign: 'center' }}>
              <button
                onClick={async () => {
                  if (!profile) return;
                  setIsLoading(true);
                  try {
                    const newDietPlan = await geminiService.generateDietPlan(profile, weightHistory);
                    setDietPlan(newDietPlan);
                  } catch (error) {
                    alert('Error generating new diet plan');
                  }
                  setIsLoading(false);
                }}
                disabled={isLoading}
                style={{
                  backgroundColor: isLoading ? '#9ca3af' : '#10b981',
                  color: 'white',
                  padding: '10px 20px',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: isLoading ? 'not-allowed' : 'pointer'
                }}
              >
                {isLoading ? '🤖 Generating...' : '🔄 Generate New Plan'}
              </button>
            </div>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <p>Generating your personalized diet plan...</p>
            <div style={{ fontSize: '24px' }}>🤖</div>
          </div>
        )}
      </div>
    );
  }

  // Weight Tracker Page
  if (currentPage === 'weight') {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', maxWidth: '800px', margin: '0 auto' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h1 style={{ color: '#333' }}>⚖️ Weight Tracker</h1>
          <button
            onClick={() => setCurrentPage('dashboard')}
            style={{ backgroundColor: '#6b7280', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            ← Back to Dashboard
          </button>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
          <div style={{ backgroundColor: '#f3f4f6', padding: '20px', borderRadius: '8px' }}>
            <h3>Current Stats</h3>
            <p><strong>Current Weight:</strong> {profile?.weight} kg</p>
            <p><strong>BMI:</strong> {profile ? (profile.weight / ((profile.height / 100) ** 2)).toFixed(1) : 'N/A'}</p>
            <p><strong>Goal:</strong> {profile?.goal?.replace('_', ' ')}</p>
            <p><strong>Entries:</strong> {weightHistory.length}</p>
          </div>

          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
            <h3>Log New Weight</h3>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const entry: WeightEntry = {
                date: formData.get('date') as string,
                weight: parseFloat(formData.get('weight') as string),
                notes: formData.get('notes') as string || undefined
              };
              addWeightEntry(entry);

              // Update profile weight if it's the latest entry
              if (profile) {
                const updatedProfile = { ...profile, weight: entry.weight };
                setProfile(updatedProfile);
              }

              (e.target as HTMLFormElement).reset();
            }}>
              <input
                name="date"
                type="date"
                defaultValue={new Date().toISOString().split('T')[0]}
                required
                style={{ width: '100%', padding: '8px', marginBottom: '10px', borderRadius: '4px', border: '1px solid #ccc' }}
              />
              <input
                name="weight"
                type="number"
                step="0.1"
                placeholder="Weight (kg)"
                required
                style={{ width: '100%', padding: '8px', marginBottom: '10px', borderRadius: '4px', border: '1px solid #ccc' }}
              />
              <textarea
                name="notes"
                placeholder="Notes (optional)"
                style={{ width: '100%', padding: '8px', marginBottom: '10px', borderRadius: '4px', border: '1px solid #ccc', minHeight: '60px' }}
              />
              <button
                type="submit"
                style={{ backgroundColor: '#8b5cf6', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer', width: '100%' }}
              >
                Log Weight
              </button>
            </form>
          </div>
        </div>

        <div>
          <h3>Weight History</h3>
          {weightHistory.length === 0 ? (
            <p style={{ color: '#666', textAlign: 'center', padding: '20px' }}>No weight entries yet. Start tracking your progress!</p>
          ) : (
            <div>
              <div style={{ display: 'grid', gap: '10px', marginBottom: '20px' }}>
                {weightHistory.slice().reverse().map((entry, index) => {
                  const prevEntry = weightHistory[weightHistory.length - index - 2];
                  const change = prevEntry ? entry.weight - prevEntry.weight : 0;

                  return (
                    <div key={entry.date} style={{ backgroundColor: 'white', padding: '15px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <h4 style={{ margin: '0 0 5px 0' }}>{new Date(entry.date).toLocaleDateString()}</h4>
                          <p style={{ margin: '0', fontSize: '18px', fontWeight: 'bold' }}>{entry.weight} kg</p>
                          {change !== 0 && (
                            <p style={{
                              margin: '5px 0 0 0',
                              fontSize: '14px',
                              color: change > 0 ? '#ef4444' : '#10b981'
                            }}>
                              {change > 0 ? '+' : ''}{change.toFixed(1)} kg
                            </p>
                          )}
                          {entry.notes && <p style={{ margin: '5px 0 0 0', fontSize: '14px', color: '#666' }}>{entry.notes}</p>}
                        </div>
                        <button
                          onClick={() => setWeightHistory(prev => prev.filter(w => w.date !== entry.date))}
                          style={{ backgroundColor: '#ef4444', color: 'white', padding: '5px 10px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>

              {weightHistory.length >= 2 && (
                <div style={{ backgroundColor: '#fef3c7', padding: '15px', borderRadius: '8px' }}>
                  <h4 style={{ color: '#92400e', margin: '0 0 10px 0' }}>📊 Progress Analysis</h4>
                  <p style={{ color: '#92400e', margin: '0' }}>
                    {(() => {
                      const firstEntry = weightHistory[0];
                      const lastEntry = weightHistory[weightHistory.length - 1];
                      const totalChange = lastEntry.weight - firstEntry.weight;
                      const days = Math.ceil((new Date(lastEntry.date).getTime() - new Date(firstEntry.date).getTime()) / (1000 * 60 * 60 * 24));

                      if (totalChange > 1) {
                        return `You've gained ${totalChange.toFixed(1)} kg over ${days} days. Consider adjusting your diet plan.`;
                      } else if (totalChange < -1) {
                        return `Great progress! You've lost ${Math.abs(totalChange).toFixed(1)} kg over ${days} days.`;
                      } else {
                        return `Your weight has been stable over ${days} days. Keep up the good work!`;
                      }
                    })()}
                  </p>

                  {weightHistory.length >= 3 && (
                    <div style={{ marginTop: '10px' }}>
                      <button
                        onClick={async () => {
                          if (!profile) return;
                          setIsLoading(true);
                          try {
                            const newDietPlan = await geminiService.generateDietPlan(profile, weightHistory);
                            setDietPlan(newDietPlan);
                            alert('Diet plan updated based on your weight progress!');
                          } catch (error) {
                            alert('Error updating diet plan');
                          }
                          setIsLoading(false);
                        }}
                        disabled={isLoading}
                        style={{
                          backgroundColor: isLoading ? '#9ca3af' : '#10b981',
                          color: 'white',
                          padding: '8px 16px',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: isLoading ? 'not-allowed' : 'pointer'
                        }}
                      >
                        {isLoading ? '🤖 Updating...' : '🔄 Update Diet Plan Based on Progress'}
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: `linear-gradient(135deg, ${theme.colors.gray[50]} 0%, ${theme.colors.gray[100]} 100%)`,
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* Header */}
      <div style={{
        background: 'white',
        padding: '20px',
        boxShadow: theme.shadows.sm,
        borderBottom: `1px solid ${theme.colors.gray[200]}`
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              fontSize: '32px',
              background: theme.gradients.primary,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              🏋️
            </div>
            <h1 style={{
              color: theme.colors.gray[900],
              margin: '0',
              fontSize: '24px',
              fontWeight: '700'
            }}>
              FitAI Dashboard
            </h1>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <span style={{
              color: theme.colors.gray[600],
              fontSize: '14px'
            }}>
              Welcome, {profile?.gender === 'male' ? 'Sir' : profile?.gender === 'female' ? 'Ma\'am' : 'Friend'}! 👋
            </span>
          </div>
        </div>
      </div>

      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '24px' }}>
        {/* Stats Cards */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '20px', marginBottom: '32px' }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '24px',
            boxShadow: theme.shadows.lg,
            border: `1px solid ${theme.colors.gray[200]}`
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
              <div style={{
                fontSize: '24px',
                background: theme.gradients.primary,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                📊
              </div>
              <h3 style={{
                margin: '0',
                color: theme.colors.gray[900],
                fontSize: '18px',
                fontWeight: '600'
              }}>
                Your Stats
              </h3>
            </div>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
              <div>
                <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[500], fontWeight: '500' }}>AGE</p>
                <p style={{ margin: '0', fontSize: '20px', fontWeight: '700', color: theme.colors.gray[900] }}>{profile?.age}</p>
              </div>
              <div>
                <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[500], fontWeight: '500' }}>HEIGHT</p>
                <p style={{ margin: '0', fontSize: '20px', fontWeight: '700', color: theme.colors.gray[900] }}>{profile?.height} cm</p>
              </div>
              <div>
                <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[500], fontWeight: '500' }}>WEIGHT</p>
                <p style={{ margin: '0', fontSize: '20px', fontWeight: '700', color: theme.colors.gray[900] }}>{profile?.weight} kg</p>
              </div>
              <div>
                <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[500], fontWeight: '500' }}>BMI</p>
                <p style={{ margin: '0', fontSize: '20px', fontWeight: '700', color: theme.colors.primary[600] }}>
                  {profile ? (profile.weight / ((profile.height / 100) ** 2)).toFixed(1) : 'N/A'}
                </p>
              </div>
            </div>
          </div>

          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '24px',
            boxShadow: theme.shadows.lg,
            border: `1px solid ${theme.colors.gray[200]}`
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
              <div style={{
                fontSize: '24px',
                background: theme.gradients.success,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                🎯
              </div>
              <h3 style={{
                margin: '0',
                color: theme.colors.gray[900],
                fontSize: '18px',
                fontWeight: '600'
              }}>
                Your Goals
              </h3>
            </div>
            <div style={{ display: 'grid', gap: '12px' }}>
              <div>
                <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[500], fontWeight: '500' }}>PRIMARY GOAL</p>
                <p style={{ margin: '0', fontSize: '16px', fontWeight: '600', color: theme.colors.gray[900], textTransform: 'capitalize' }}>
                  {profile?.goal?.replace('_', ' ')}
                </p>
              </div>
              <div>
                <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[500], fontWeight: '500' }}>ACTIVITY LEVEL</p>
                <p style={{ margin: '0', fontSize: '16px', fontWeight: '600', color: theme.colors.gray[900], textTransform: 'capitalize' }}>
                  {profile?.activityLevel?.replace('_', ' ')}
                </p>
              </div>
              <div>
                <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[500], fontWeight: '500' }}>BODY TYPE</p>
                <p style={{ margin: '0', fontSize: '16px', fontWeight: '600', color: theme.colors.success[600], textTransform: 'capitalize' }}>
                  {profile?.bodyType}
                </p>
              </div>
            </div>
          </div>
        </div>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
          <h3 style={{ color: '#3b82f6' }}>📱 Calorie Tracker</h3>
          <p>Track your daily food intake with AI-powered food recognition</p>
          <p style={{ fontSize: '14px', color: '#666' }}>Today: {getTodayCalories()} / {dietPlan?.dailyCalories || 2000} cal</p>
          <button
            onClick={() => setCurrentPage('calories')}
            style={{ backgroundColor: '#3b82f6', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            Track Food
          </button>
        </div>

        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
          <h3 style={{ color: '#f59e0b' }}>💪 Workout Plans</h3>
          <p>Get personalized workout recommendations based on your goals</p>
          <p style={{ fontSize: '14px', color: '#666' }}>{workoutPlan ? `${workoutPlan.name} (${workoutPlan.duration} min)` : 'Generating...'}</p>
          <button
            onClick={() => setCurrentPage('workouts')}
            style={{ backgroundColor: '#f59e0b', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            View Workouts
          </button>
        </div>

        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
          <h3 style={{ color: '#10b981' }}>🍽️ Diet Plans</h3>
          <p>AI-generated meal plans tailored to your body type and goals</p>
          <p style={{ fontSize: '14px', color: '#666' }}>{dietPlan ? `${dietPlan.meals?.length || 0} meals planned` : 'Generating...'}</p>
          <button
            onClick={() => setCurrentPage('diet')}
            style={{ backgroundColor: '#10b981', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            View Diet Plan
          </button>
        </div>

        <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
          <h3 style={{ color: '#8b5cf6' }}>⚖️ Weight Tracker</h3>
          <p>Log your weekly weight and track progress</p>
          <p style={{ fontSize: '14px', color: '#666' }}>Entries: {weightHistory.length}</p>
          <button
            onClick={() => setCurrentPage('weight')}
            style={{ backgroundColor: '#8b5cf6', color: 'white', padding: '8px 16px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            Log Weight
          </button>
        </div>
      </div>

      <div style={{ textAlign: 'center', marginTop: '30px' }}>
        <button
          style={{
            backgroundColor: '#ef4444',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
          onClick={() => {
            setProfile(null);
            setCurrentPage('welcome');
          }}
        >
          Reset Profile
        </button>
      </div>
    </div>
  );
}

export default App;
