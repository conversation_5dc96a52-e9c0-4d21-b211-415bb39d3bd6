import React, { useState } from 'react';
import { useUserStore } from './store/userStore';

function App() {
  const { profile, isProfileComplete, setProfile } = useUserStore();
  const [showTest, setShowTest] = useState(true);

  if (showTest) {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1 style={{ color: '#333' }}>FitAI - Fitness App</h1>
        <p>Welcome to your AI-powered fitness companion!</p>
        <button
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
          onClick={() => {
            const dummyProfile = {
              age: 25,
              gender: 'male' as const,
              height: 175,
              weight: 70,
              activityLevel: 'moderately_active' as const,
              goal: 'lose_weight' as const,
              bodyType: 'mesomorph' as const,
              medicalConditions: [],
              allergies: [],
              dietaryPreferences: []
            };
            setProfile(dummyProfile);
            setShowTest(false);
          }}
        >
          Create Profile & Continue
        </button>
        <button
          style={{
            backgroundColor: '#6b7280',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
          onClick={() => alert('Store test: ' + JSON.stringify({ profile, isProfileComplete }))}
        >
          Test Store
        </button>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#333' }}>FitAI Dashboard</h1>
      <div style={{ backgroundColor: '#f3f4f6', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
        <h2>Profile Information</h2>
        <p><strong>Age:</strong> {profile?.age}</p>
        <p><strong>Gender:</strong> {profile?.gender}</p>
        <p><strong>Height:</strong> {profile?.height} cm</p>
        <p><strong>Weight:</strong> {profile?.weight} kg</p>
        <p><strong>Goal:</strong> {profile?.goal?.replace('_', ' ')}</p>
        <p><strong>Body Type:</strong> {profile?.bodyType}</p>
      </div>
      <button
        style={{
          backgroundColor: '#ef4444',
          color: 'white',
          padding: '10px 20px',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}
        onClick={() => {
          setShowTest(true);
        }}
      >
        Back to Test
      </button>
    </div>
  );
}

export default App;
